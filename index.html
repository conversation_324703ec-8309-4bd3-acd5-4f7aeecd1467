<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Assistant - Chat & Image Generation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: Arial, sans-serif;
        }

        body {
            background-color: #f0f2f5;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            display: flex;
            gap: 20px;
            flex: 1;
        }

        .chat-section, .image-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 20px;
            padding: 10px;
        }

        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
            max-width: 80%;
        }

        .user-message {
            background-color: #007bff;
            color: white;
            margin-left: auto;
        }

        .bot-message {
            background-color: #e9ecef;
            color: black;
        }

        .input-area {
            display: flex;
            gap: 10px;
        }

        input[type="text"], textarea {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }

        button {
            padding: 12px 24px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: #0056b3;
        }

        .image-result {
            margin-top: 20px;
            text-align: center;
        }

        .image-result img {
            max-width: 100%;
            border-radius: 8px;
        }

        h2 {
            margin-bottom: 20px;
            color: #333;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 10px 0;
        }

        .loading::after {
            content: "Loading...";
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="chat-section">
            <h2>Chat with AI</h2>
            <div class="chat-messages" id="chatMessages"></div>
            <div class="loading" id="chatLoading"></div>
            <div class="input-area">
                <input type="text" id="chatInput" placeholder="Ask me anything...">
                <button onclick="sendMessage()">Send</button>
            </div>
        </div>
        <div class="image-section">
            <h2>Generate Image</h2>
            <div class="input-area">
                <textarea id="imagePrompt" placeholder="Describe the image you want to generate..." rows="3"></textarea>
            </div>
            <div class="input-area" style="margin-top: 10px;">
                <button onclick="generateImage()">Generate Image</button>
            </div>
            <div class="loading" id="imageLoading"></div>
            <div class="image-result" id="imageResult"></div>
        </div>
    </div>

    <script>        const API_KEY = 'AIzaSyBShVpF5xSPxacVe_EfHvBi2TLJZClwFek';
        const API_URL = 'https://generativelanguage.googleapis.com/v1/models/gemini-pro';

        async function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();
            if (!message) return;

            // Add user message to chat
            addMessage(message, true);
            input.value = '';

            document.getElementById('chatLoading').style.display = 'block';            try {
                const response = await fetch(`${API_URL}:generateContent?key=${API_KEY}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{
                                text: message
                            }]
                        }]
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    console.error('API Error:', errorData);
                    throw new Error(errorData.error?.message || 'API request failed');
                }

                const data = await response.json();
                console.log('API Response:', data);

                if (!data.candidates || !data.candidates[0]?.content?.parts?.[0]?.text) {
                    throw new Error('Invalid or empty response from AI');
                }

                const botResponse = data.candidates[0].content.parts[0].text;
                
                addMessage(botResponse, false);
            } catch (error) {
                console.error('Error:', error);
                let errorMessage = 'Sorry, I encountered an error. ';
                if (error.message.includes('API key')) {
                    errorMessage += 'There might be an issue with the API key.';
                } else if (error.message.includes('network')) {
                    errorMessage += 'Please check your internet connection.';
                } else {
                    errorMessage += 'Please try again in a moment.';
                }
                addMessage(errorMessage, false);
            }

            document.getElementById('chatLoading').style.display = 'none';
        }

        async function generateImage() {
            const prompt = document.getElementById('imagePrompt').value.trim();
            if (!prompt) return;

            const imageResult = document.getElementById('imageResult');
            document.getElementById('imageLoading').style.display = 'block';
            imageResult.innerHTML = '';

            try {
                // Note: This is a simplified version. The actual image generation would require
                // integration with an image generation API like DALL-E or Stable Diffusion
                // as Gemini Pro currently doesn't support image generation
                addMessage('Image generation is not yet implemented in this demo. You would need to integrate with a specific image generation API for this feature.', false);
            } catch (error) {
                addMessage('Sorry, I encountered an error generating the image.', false);
                console.error('Error:', error);
            }

            document.getElementById('imageLoading').style.display = 'none';
        }

        function addMessage(text, isUser) {
            const messagesDiv = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user-message' : 'bot-message'}`;
            messageDiv.textContent = text;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // Handle Enter key in chat input
        document.getElementById('chatInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                sendMessage();
            }
        });
    </script>
</body>
</html>