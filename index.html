<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Assistant - Chat & Image Generation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: Arial, sans-serif;
        }

        body {
            background-color: #f0f2f5;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            display: flex;
            gap: 20px;
            flex: 1;
        }

        .chat-section, .image-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 20px;
            padding: 10px;
        }

        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
            max-width: 80%;
        }

        .user-message {
            background-color: #007bff;
            color: white;
            margin-left: auto;
        }

        .bot-message {
            background-color: #e9ecef;
            color: black;
        }

        .input-area {
            display: flex;
            gap: 10px;
        }

        input[type="text"], textarea {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }

        button {
            padding: 12px 24px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: #0056b3;
        }

        .image-result {
            margin-top: 20px;
            text-align: center;
        }

        .image-result img {
            max-width: 100%;
            border-radius: 8px;
        }

        h2 {
            margin-bottom: 20px;
            color: #333;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 10px 0;
        }

        .loading::after {
            content: "Loading...";
            color: #666;
        }

        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }

        .success-message {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }

        .api-status {
            position: fixed;
            top: 10px;
            right: 10px;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }

        .api-status.connected {
            background-color: #d4edda;
            color: #155724;
        }

        .api-status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
                margin: 10px;
                padding: 10px;
            }

            .chat-section, .image-section {
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="api-status disconnected" id="apiStatus">API Not Configured</div>
    <div class="container">
        <div class="chat-section">
            <h2>Chat with AI</h2>
            <div class="chat-messages" id="chatMessages"></div>
            <div class="loading" id="chatLoading"></div>
            <div class="input-area">
                <input type="text" id="chatInput" placeholder="Ask me anything...">
                <button onclick="sendMessage()">Send</button>
            </div>
        </div>
        <div class="image-section">
            <h2>Generate Image</h2>
            <div class="input-area">
                <textarea id="imagePrompt" placeholder="Describe the image you want to generate..." rows="3"></textarea>
            </div>
            <div class="input-area" style="margin-top: 10px;">
                <button onclick="generateImage()">Generate Image</button>
            </div>
            <div class="loading" id="imageLoading"></div>
            <div class="image-result" id="imageResult"></div>
        </div>
    </div>

    <!-- Load configuration -->
    <script src="config.js"></script>
    <script>
        // Check if CONFIG is loaded, if not use inline config
        if (typeof CONFIG === 'undefined') {
            const CONFIG = {
                GEMINI_API_KEY: 'AIzaSyBSQcugRBiixUDoeNKk6sJ3j3xiIXakw2A',
                GEMINI_API_URL: 'https://generativelanguage.googleapis.com/v1/models/gemini-2.0-flash-exp',
                UNSPLASH_ACCESS_KEY: 'YOUR_UNSPLASH_ACCESS_KEY_HERE',
                FALLBACK_IMAGE_URL: 'https://picsum.photos'
            };
        }

        async function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();
            if (!message) return;

            // Add user message to chat
            addMessage(message, true);
            input.value = '';

            document.getElementById('chatLoading').style.display = 'block';

            try {
                // Check if API key is configured
                if (!CONFIG.GEMINI_API_KEY || CONFIG.GEMINI_API_KEY === 'YOUR_GEMINI_API_KEY_HERE') {
                    throw new Error('API key not configured. Please add your Gemini API key.');
                }

                const response = await fetch(`${CONFIG.GEMINI_API_URL}:generateContent?key=${CONFIG.GEMINI_API_KEY}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{
                                text: message
                            }]
                        }]
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    console.error('API Error:', errorData);
                    throw new Error(errorData.error?.message || 'API request failed');
                }

                const data = await response.json();
                console.log('API Response:', data);

                if (!data.candidates || !data.candidates[0]?.content?.parts?.[0]?.text) {
                    throw new Error('Invalid or empty response from AI');
                }

                const botResponse = data.candidates[0].content.parts[0].text;
                addMessage(botResponse, false);

            } catch (error) {
                console.error('Error:', error);
                let errorMessage = 'Sorry, I encountered an error. ';

                if (error.message.includes('API key')) {
                    errorMessage += 'Please configure your API key in the code.';
                } else if (error.message.includes('network') || error.message.includes('fetch')) {
                    errorMessage += 'Please check your internet connection.';
                } else if (error.message.includes('quota') || error.message.includes('limit')) {
                    errorMessage += 'API quota exceeded. Please try again later.';
                } else {
                    errorMessage += 'Please try again in a moment.';
                }

                addMessage(errorMessage, false);
            }

            document.getElementById('chatLoading').style.display = 'none';
        }

        async function generateImage() {
            const prompt = document.getElementById('imagePrompt').value.trim();
            if (!prompt) {
                addMessage('Please enter a description for the image you want to generate.', false);
                return;
            }

            const imageResult = document.getElementById('imageResult');
            document.getElementById('imageLoading').style.display = 'block';
            imageResult.innerHTML = '';

            try {
                // Using Unsplash API for demo - provides random images based on search terms
                const searchQuery = encodeURIComponent(prompt.split(' ').slice(0, 3).join(' '));
                const response = await fetch(`https://api.unsplash.com/photos/random?query=${searchQuery}&client_id=${CONFIG.UNSPLASH_ACCESS_KEY}`);

                if (!response.ok) {
                    // Fallback: Use a placeholder image service
                    const fallbackImageUrl = `https://picsum.photos/400/300?random=${Date.now()}`;
                    imageResult.innerHTML = `
                        <div>
                            <p><strong>Generated Image for:</strong> "${prompt}"</p>
                            <img src="${fallbackImageUrl}" alt="Generated image" style="max-width: 100%; border-radius: 8px; margin-top: 10px;">
                            <p style="font-size: 12px; color: #666; margin-top: 10px;">
                                Note: This is a placeholder image. For real AI image generation, integrate with DALL-E, Midjourney, or Stable Diffusion APIs.
                            </p>
                        </div>
                    `;
                } else {
                    const data = await response.json();
                    imageResult.innerHTML = `
                        <div>
                            <p><strong>Image for:</strong> "${prompt}"</p>
                            <img src="${data.urls.regular}" alt="${data.alt_description || prompt}" style="max-width: 100%; border-radius: 8px; margin-top: 10px;">
                            <p style="font-size: 12px; color: #666; margin-top: 10px;">
                                Photo by ${data.user.name} on Unsplash
                            </p>
                        </div>
                    `;
                }

                // Clear the input
                document.getElementById('imagePrompt').value = '';

            } catch (error) {
                console.error('Image generation error:', error);

                // Fallback to placeholder image
                const fallbackImageUrl = `https://picsum.photos/400/300?random=${Date.now()}`;
                imageResult.innerHTML = `
                    <div>
                        <p><strong>Generated Image for:</strong> "${prompt}"</p>
                        <img src="${fallbackImageUrl}" alt="Generated image" style="max-width: 100%; border-radius: 8px; margin-top: 10px;">
                        <p style="font-size: 12px; color: #666; margin-top: 10px;">
                            Note: Using placeholder image. For real AI image generation, configure proper API keys.
                        </p>
                    </div>
                `;
            }

            document.getElementById('imageLoading').style.display = 'none';
        }

        function addMessage(text, isUser) {
            const messagesDiv = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user-message' : 'bot-message'}`;
            messageDiv.innerHTML = text.replace(/\n/g, '<br>'); // Handle line breaks
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // Handle Enter key in chat input
        document.getElementById('chatInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                sendMessage();
            }
        });

        // Handle Enter key in image prompt (Ctrl+Enter to generate)
        document.getElementById('imagePrompt').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && e.ctrlKey) {
                e.preventDefault();
                generateImage();
            }
        });

        // Update API status indicator
        function updateApiStatus() {
            const statusElement = document.getElementById('apiStatus');
            if (CONFIG.GEMINI_API_KEY && CONFIG.GEMINI_API_KEY !== 'YOUR_GEMINI_API_KEY_HERE') {
                statusElement.textContent = 'API Connected';
                statusElement.className = 'api-status connected';
            } else {
                statusElement.textContent = 'API Not Configured';
                statusElement.className = 'api-status disconnected';
            }
        }

        // Add welcome message on page load
        window.addEventListener('load', function() {
            updateApiStatus();
            addMessage('Hello! I\'m your AI assistant powered by Gemini 2.0 Flash. I can answer questions and help generate images. How can I help you today?', false);
        });
    </script>
</body>
</html>