# AI Chatbot Website

A simple AI chatbot website built with HTML, CSS, and JavaScript that can answer questions and generate images.

## Features

- 💬 **Chat with AI**: Ask questions and get intelligent responses
- 🖼️ **Image Generation**: Generate images based on text descriptions
- 📱 **Responsive Design**: Works on desktop and mobile devices
- ⚡ **Real-time Responses**: Fast and interactive user experience

## Setup Instructions

### 1. Get API Keys

#### For Chat Functionality (Required):
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key for Gemini
3. Copy the API key

#### For Better Image Search (Optional):
1. Go to [Unsplash Developers](https://unsplash.com/developers)
2. Create a new application
3. Copy the Access Key

### 2. Configure the Application

1. Open `config.js` file
2. Replace `YOUR_GEMINI_API_KEY_HERE` with your actual Gemini API key
3. (Optional) Replace `YOUR_UNSPLASH_ACCESS_KEY_HERE` with your Unsplash access key

### 3. Run the Application

1. Open `index.html` in your web browser
2. Start chatting with the AI!

## Usage

### Chat Features:
- Type your question in the chat input
- Press Enter or click "Send" to get a response
- The AI can answer questions, help with coding, explain concepts, and more

### Image Generation:
- Enter a description of the image you want
- Click "Generate Image" or press Ctrl+Enter
- The system will find or generate an image based on your description

## Technical Details

- **Frontend**: Pure HTML, CSS, and JavaScript (no frameworks required)
- **AI API**: Google Gemini Pro for text generation
- **Image API**: Unsplash API with fallback to placeholder images
- **No Backend Required**: Runs entirely in the browser

## Security Notes

⚠️ **Important**: This is a demo application. In a production environment:
- Store API keys securely on the server side
- Implement proper authentication
- Add rate limiting
- Validate and sanitize all inputs

## Troubleshooting

### Chat not working?
- Check if you've added your Gemini API key in `config.js`
- Verify your internet connection
- Check the browser console for error messages

### Images not loading?
- The app uses fallback placeholder images if APIs are not configured
- For better results, add your Unsplash API key

## Browser Compatibility

- Chrome (recommended)
- Firefox
- Safari
- Edge

## License

This project is open source and available under the MIT License.
