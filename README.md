# AI Chatbot Website

A simple AI chatbot website built with HTML, CSS, and JavaScript that can answer questions and generate images.

## Features

- 💬 **Chat with AI**: Ask questions and get intelligent responses from multiple Gemini models
- 🖼️ **AI Image Generation**: Create images using Veo 3 via Gemini 2.0+
- 🎬 **AI Video Generation**: Generate videos using Veo 3 via Gemini 2.0+
- 🔄 **Model Selection**: Choose between Gemini 1.5, 2.0, and 2.5 models
- 📱 **Responsive Design**: Works on desktop and mobile devices
- ⚡ **Real-time Responses**: Fast and interactive user experience

## Setup Instructions

### 1. Get API Keys

#### For Chat Functionality (Required):
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key for Gemini
3. Copy the API key

#### For Better Image Search (Optional):
1. Go to [Unsplash Developers](https://unsplash.com/developers)
2. Create a new application
3. Copy the Access Key

### 2. Configure the Application

1. Open `config.js` file
2. Replace `YOUR_GEMINI_API_KEY_HERE` with your actual Gemini API key
3. (Optional) Replace `YOUR_UNSPLASH_ACCESS_KEY_HERE` with your Unsplash access key

### 3. Run the Application

1. Open `index.html` in your web browser
2. Start chatting with the AI!

## Usage

### Chat Features:
- Select your preferred Gemini model from the dropdown
- Type your question in the chat input
- Press Enter or click "Send" to get a response
- The AI can answer questions, help with coding, explain concepts, and more

### Image Generation with Veo 3:
- Switch to Gemini 2.0 Flash or 2.5 Flash for best results
- Enter a description of the image you want
- Choose your preferred style (Realistic, Artistic, Cartoon, Abstract)
- Click "Generate Image" or press Ctrl+Enter
- The system will generate an AI image using Veo 3 technology

### Video Generation with Veo 3:
- Switch to Gemini 2.0 Flash or 2.5 Flash (required)
- Click the "Generate Video" tab
- Enter a description of the video you want
- Choose duration (5-30 seconds) and style (Cinematic, Documentary, Animation, Time-lapse)
- Click "Generate Video" or press Ctrl+Enter
- The system will generate an AI video using Veo 3 technology

## Technical Details

- **Frontend**: Pure HTML, CSS, and JavaScript (no frameworks required)
- **AI API**: Google Gemini Pro for text generation
- **Image API**: Unsplash API with fallback to placeholder images
- **No Backend Required**: Runs entirely in the browser

## Security Notes

⚠️ **Important**: This is a demo application. In a production environment:
- Store API keys securely on the server side
- Implement proper authentication
- Add rate limiting
- Validate and sanitize all inputs

## Troubleshooting

### Chat not working?
- Check if you've added your Gemini API key in `config.js`
- Verify your internet connection
- Check the browser console for error messages

### Images not loading?
- The app uses fallback placeholder images if APIs are not configured
- For better results, add your Unsplash API key

## Browser Compatibility

- Chrome (recommended)
- Firefox
- Safari
- Edge

## License

This project is open source and available under the MIT License.
