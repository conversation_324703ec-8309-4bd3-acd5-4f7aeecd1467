// Configuration file for AI Chatbot
// Replace the placeholder values with your actual API keys

const CONFIG = {
    // Google Gemini API Configuration
    // Get your API key from: https://makersuite.google.com/app/apikey
    GEMINI_API_KEY: 'YOUR_GEMINI_API_KEY_HERE',
    GEMINI_API_URL: 'https://generativelanguage.googleapis.com/v1/models/gemini-pro',
    
    // Unsplash API Configuration (optional - for better image search)
    // Get your access key from: https://unsplash.com/developers
    UNSPLASH_ACCESS_KEY: 'YOUR_UNSPLASH_ACCESS_KEY_HERE',
    
    // Fallback image service (no API key required)
    FALLBACK_IMAGE_URL: 'https://picsum.photos',
    
    // App Settings
    MAX_MESSAGE_LENGTH: 1000,
    MAX_PROMPT_LENGTH: 200,
    TYPING_DELAY: 1000 // milliseconds
};

// Export for use in main script
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
}
