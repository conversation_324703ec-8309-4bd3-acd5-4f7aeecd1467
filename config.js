// Configuration file for AI Chatbot
// Replace the placeholder values with your actual API keys

const CONFIG = {
    // Google Gemini API Configuration
    // Get your API key from: https://makersuite.google.com/app/apikey
    GEMINI_API_KEY: 'AIzaSyBSQcugRBiixUDoeNKk6sJ3j3xiIXakw2A',
    GEMINI_API_URL: 'https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash',

    // Available Gemini Models
    AVAILABLE_MODELS: {
        'gemini-1.5-flash': 'Gemini 1.5 Flash (Fast & Efficient)',
        'gemini-1.5-pro': 'Gemini 1.5 Pro (Advanced)',
        'gemini-2.0-flash-exp': 'Gemini 2.0 Flash (Experimental)',
        'gemini-2.5-flash-exp': 'Gemini 2.5 Flash (Latest)'
    },

    // App Settings
    MAX_MESSAGE_LENGTH: 1000,
    TYPING_DELAY: 1000 // milliseconds
};

// Export for use in main script
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
}
